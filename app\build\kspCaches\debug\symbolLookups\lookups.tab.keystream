  Application android.app  Build android.app.Activity  Bundle android.app.Activity  RequiresApi android.app.Activity  Context android.content  Build android.content.Context  Bundle android.content.Context  RequiresApi android.content.Context  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  RequiresApi android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  
VERSION_CODES android.os.Build  O android.os.Build.VERSION_CODES  Build  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  RequiresApi  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Build #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  RequiresApi #androidx.activity.ComponentActivity  Build -androidx.activity.ComponentActivity.Companion  RequiresApi androidx.annotation  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  RequiresApi #androidx.core.app.ComponentActivity  	DataStore androidx.datastore.core  Preferences #androidx.datastore.preferences.core  	ViewModel androidx.lifecycle  AuthUiState androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  CompletionRepository androidx.lifecycle.ViewModel  CreateHabitUiState androidx.lifecycle.ViewModel  Double androidx.lifecycle.ViewModel  EnhancedFrequency androidx.lifecycle.ViewModel  Float androidx.lifecycle.ViewModel  Habit androidx.lifecycle.ViewModel  HabitFrequency androidx.lifecycle.ViewModel  HabitRepository androidx.lifecycle.ViewModel  HabitSection androidx.lifecycle.ViewModel  HabitSectionRepository androidx.lifecycle.ViewModel  	HabitType androidx.lifecycle.ViewModel  HabitWithCompletions androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  	LocalDate androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MainUiState androidx.lifecycle.ViewModel  ManageSectionsUiState androidx.lifecycle.ViewModel  Map androidx.lifecycle.ViewModel  
ReminderState androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  Unit androidx.lifecycle.ViewModel  UserPreferencesRepository androidx.lifecycle.ViewModel  WeekInfo androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  HabitsApplication com.example.habits9  Boolean com.example.habits9.data  
Completion com.example.habits9.data  CompletionRepository com.example.habits9.data  Double com.example.habits9.data  EnhancedFrequency com.example.habits9.data  
FrequencyType com.example.habits9.data  Habit com.example.habits9.data  HabitRepository com.example.habits9.data  HabitSection com.example.habits9.data  HabitSectionRepository com.example.habits9.data  	HabitType com.example.habits9.data  Int com.example.habits9.data  List com.example.habits9.data  Long com.example.habits9.data  NumericalHabitType com.example.habits9.data  String com.example.habits9.data  UserPreferencesRepository com.example.habits9.data  	dataStore com.example.habits9.data  
Completion -com.example.habits9.data.CompletionRepository  FirebaseAuth -com.example.habits9.data.CompletionRepository  FirebaseFirestore -com.example.habits9.data.CompletionRepository  Flow -com.example.habits9.data.CompletionRepository  Inject -com.example.habits9.data.CompletionRepository  List -com.example.habits9.data.CompletionRepository  Long -com.example.habits9.data.CompletionRepository  String -com.example.habits9.data.CompletionRepository  
Completion 7com.example.habits9.data.CompletionRepository.Companion  FirebaseAuth 7com.example.habits9.data.CompletionRepository.Companion  FirebaseFirestore 7com.example.habits9.data.CompletionRepository.Companion  Flow 7com.example.habits9.data.CompletionRepository.Companion  Inject 7com.example.habits9.data.CompletionRepository.Companion  List 7com.example.habits9.data.CompletionRepository.Companion  Long 7com.example.habits9.data.CompletionRepository.Companion  String 7com.example.habits9.data.CompletionRepository.Companion  Boolean com.example.habits9.data.Habit  Double com.example.habits9.data.Habit  
FrequencyType com.example.habits9.data.Habit  	HabitType com.example.habits9.data.Habit  Int com.example.habits9.data.Habit  List com.example.habits9.data.Habit  Long com.example.habits9.data.Habit  NumericalHabitType com.example.habits9.data.Habit  String com.example.habits9.data.Habit  FirebaseAuth (com.example.habits9.data.HabitRepository  FirebaseFirestore (com.example.habits9.data.HabitRepository  Flow (com.example.habits9.data.HabitRepository  Habit (com.example.habits9.data.HabitRepository  Inject (com.example.habits9.data.HabitRepository  List (com.example.habits9.data.HabitRepository  Long (com.example.habits9.data.HabitRepository  FirebaseAuth 2com.example.habits9.data.HabitRepository.Companion  FirebaseFirestore 2com.example.habits9.data.HabitRepository.Companion  Flow 2com.example.habits9.data.HabitRepository.Companion  Habit 2com.example.habits9.data.HabitRepository.Companion  Inject 2com.example.habits9.data.HabitRepository.Companion  List 2com.example.habits9.data.HabitRepository.Companion  Long 2com.example.habits9.data.HabitRepository.Companion  FirebaseAuth /com.example.habits9.data.HabitSectionRepository  FirebaseFirestore /com.example.habits9.data.HabitSectionRepository  Flow /com.example.habits9.data.HabitSectionRepository  HabitSection /com.example.habits9.data.HabitSectionRepository  Inject /com.example.habits9.data.HabitSectionRepository  List /com.example.habits9.data.HabitSectionRepository  FirebaseAuth 9com.example.habits9.data.HabitSectionRepository.Companion  FirebaseFirestore 9com.example.habits9.data.HabitSectionRepository.Companion  Flow 9com.example.habits9.data.HabitSectionRepository.Companion  HabitSection 9com.example.habits9.data.HabitSectionRepository.Companion  Inject 9com.example.habits9.data.HabitSectionRepository.Companion  List 9com.example.habits9.data.HabitSectionRepository.Companion  ApplicationContext 2com.example.habits9.data.UserPreferencesRepository  Context 2com.example.habits9.data.UserPreferencesRepository  Flow 2com.example.habits9.data.UserPreferencesRepository  Inject 2com.example.habits9.data.UserPreferencesRepository  String 2com.example.habits9.data.UserPreferencesRepository  DatabaseModule com.example.habits9.di  SingletonComponent com.example.habits9.di  CompletionRepository %com.example.habits9.di.DatabaseModule  FirebaseAuth %com.example.habits9.di.DatabaseModule  FirebaseFirestore %com.example.habits9.di.DatabaseModule  HabitRepository %com.example.habits9.di.DatabaseModule  HabitSectionRepository %com.example.habits9.di.DatabaseModule  Provides %com.example.habits9.di.DatabaseModule  	Singleton %com.example.habits9.di.DatabaseModule  Boolean com.example.habits9.ui  Double com.example.habits9.ui  Float com.example.habits9.ui  HabitWithCompletions com.example.habits9.ui  Int com.example.habits9.ui  List com.example.habits9.ui  Long com.example.habits9.ui  MainUiState com.example.habits9.ui  
MainViewModel com.example.habits9.ui  Map com.example.habits9.ui  String com.example.habits9.ui  WeekInfo com.example.habits9.ui  com com.example.habits9.ui  Boolean $com.example.habits9.ui.MainViewModel  CompletionRepository $com.example.habits9.ui.MainViewModel  Double $com.example.habits9.ui.MainViewModel  Float $com.example.habits9.ui.MainViewModel  Habit $com.example.habits9.ui.MainViewModel  HabitRepository $com.example.habits9.ui.MainViewModel  	HabitType $com.example.habits9.ui.MainViewModel  HabitWithCompletions $com.example.habits9.ui.MainViewModel  Inject $com.example.habits9.ui.MainViewModel  Int $com.example.habits9.ui.MainViewModel  List $com.example.habits9.ui.MainViewModel  	LocalDate $com.example.habits9.ui.MainViewModel  Long $com.example.habits9.ui.MainViewModel  MainUiState $com.example.habits9.ui.MainViewModel  Map $com.example.habits9.ui.MainViewModel  	StateFlow $com.example.habits9.ui.MainViewModel  String $com.example.habits9.ui.MainViewModel  WeekInfo $com.example.habits9.ui.MainViewModel  com $com.example.habits9.ui.MainViewModel  AuthUiState com.example.habits9.ui.auth  
AuthViewModel com.example.habits9.ui.auth  DarkAccentPrimary com.example.habits9.ui.auth  DarkAccentPrimaryLogo com.example.habits9.ui.auth  DarkBackground com.example.habits9.ui.auth  DarkBackgroundDarker com.example.habits9.ui.auth  
DarkIconColor com.example.habits9.ui.auth  DarkSurfaceVariant com.example.habits9.ui.auth  DarkTextPrimary com.example.habits9.ui.auth  DarkTextSecondary com.example.habits9.ui.auth  LightAccentPrimary com.example.habits9.ui.auth  LightAccentPrimaryLogo com.example.habits9.ui.auth  LightBackground com.example.habits9.ui.auth  LightBackgroundDarker com.example.habits9.ui.auth  
LightError com.example.habits9.ui.auth  LightIconColor com.example.habits9.ui.auth  LightSurfaceVariant com.example.habits9.ui.auth  LightTextPrimary com.example.habits9.ui.auth  LightTextSecondary com.example.habits9.ui.auth  String com.example.habits9.ui.auth  Unit com.example.habits9.ui.auth  AuthUiState )com.example.habits9.ui.auth.AuthViewModel  Inject )com.example.habits9.ui.auth.AuthViewModel  	StateFlow )com.example.habits9.ui.auth.AuthViewModel  String )com.example.habits9.ui.auth.AuthViewModel  Unit )com.example.habits9.ui.auth.AuthViewModel  
AccentPrimary !com.example.habits9.ui.components  BackgroundDark !com.example.habits9.ui.components  DarkBackground !com.example.habits9.ui.components  DividerColor !com.example.habits9.ui.components  SurfaceVariantDark !com.example.habits9.ui.components  TextPrimary !com.example.habits9.ui.components  
TextSecondary !com.example.habits9.ui.components  CreateHabitUiState "com.example.habits9.ui.createhabit  CreateHabitViewModel "com.example.habits9.ui.createhabit  HabitFrequency "com.example.habits9.ui.createhabit  Int "com.example.habits9.ui.createhabit  
ReminderState "com.example.habits9.ui.createhabit  String "com.example.habits9.ui.createhabit  Unit "com.example.habits9.ui.createhabit  com "com.example.habits9.ui.createhabit  CreateHabitUiState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  EnhancedFrequency 7com.example.habits9.ui.createhabit.CreateHabitViewModel  HabitFrequency 7com.example.habits9.ui.createhabit.CreateHabitViewModel  HabitSection 7com.example.habits9.ui.createhabit.CreateHabitViewModel  HabitSectionRepository 7com.example.habits9.ui.createhabit.CreateHabitViewModel  Inject 7com.example.habits9.ui.createhabit.CreateHabitViewModel  Int 7com.example.habits9.ui.createhabit.CreateHabitViewModel  
ReminderState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  	StateFlow 7com.example.habits9.ui.createhabit.CreateHabitViewModel  String 7com.example.habits9.ui.createhabit.CreateHabitViewModel  Unit 7com.example.habits9.ui.createhabit.CreateHabitViewModel  com 7com.example.habits9.ui.createhabit.CreateHabitViewModel  
AccentPrimary ,com.example.habits9.ui.createmeasurablehabit  DarkBackground ,com.example.habits9.ui.createmeasurablehabit  DividerColor ,com.example.habits9.ui.createmeasurablehabit  SurfaceVariantDark ,com.example.habits9.ui.createmeasurablehabit  TextPrimary ,com.example.habits9.ui.createmeasurablehabit  
TextSecondary ,com.example.habits9.ui.createmeasurablehabit  
AccentPrimary 'com.example.habits9.ui.createyesnohabit  DarkBackground 'com.example.habits9.ui.createyesnohabit  DividerColor 'com.example.habits9.ui.createyesnohabit  SurfaceVariantDark 'com.example.habits9.ui.createyesnohabit  TextPrimary 'com.example.habits9.ui.createyesnohabit  
TextSecondary 'com.example.habits9.ui.createyesnohabit  
AccentPrimary com.example.habits9.ui.details  DarkBackground com.example.habits9.ui.details  DividerColor com.example.habits9.ui.details  SurfaceVariantDark com.example.habits9.ui.details  TextPrimary com.example.habits9.ui.details  
TextSecondary com.example.habits9.ui.details  
AccentPrimary )com.example.habits9.ui.habittypeselection  DarkBackground )com.example.habits9.ui.habittypeselection  SurfaceVariantDark )com.example.habits9.ui.habittypeselection  TextPrimary )com.example.habits9.ui.habittypeselection  
TextSecondary )com.example.habits9.ui.habittypeselection  
AccentPrimary com.example.habits9.ui.home  BackgroundDark com.example.habits9.ui.home  DividerColor com.example.habits9.ui.home  SurfaceVariantDark com.example.habits9.ui.home  TextPrimary com.example.habits9.ui.home  
TextSecondary com.example.habits9.ui.home  
AccentPrimary %com.example.habits9.ui.managesections  DarkBackground %com.example.habits9.ui.managesections  DividerColor %com.example.habits9.ui.managesections  Int %com.example.habits9.ui.managesections  ManageSectionsUiState %com.example.habits9.ui.managesections  ManageSectionsViewModel %com.example.habits9.ui.managesections  
SectionColors %com.example.habits9.ui.managesections  String %com.example.habits9.ui.managesections  SurfaceVariantDark %com.example.habits9.ui.managesections  TextPrimary %com.example.habits9.ui.managesections  
TextSecondary %com.example.habits9.ui.managesections  HabitSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  HabitSectionRepository =com.example.habits9.ui.managesections.ManageSectionsViewModel  Inject =com.example.habits9.ui.managesections.ManageSectionsViewModel  Int =com.example.habits9.ui.managesections.ManageSectionsViewModel  ManageSectionsUiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  	StateFlow =com.example.habits9.ui.managesections.ManageSectionsViewModel  String =com.example.habits9.ui.managesections.ManageSectionsViewModel  
AccentPrimary com.example.habits9.ui.settings  DarkBackground com.example.habits9.ui.settings  SettingsViewModel com.example.habits9.ui.settings  String com.example.habits9.ui.settings  SurfaceVariantDark com.example.habits9.ui.settings  TextPrimary com.example.habits9.ui.settings  
TextSecondary com.example.habits9.ui.settings  Inject 1com.example.habits9.ui.settings.SettingsViewModel  	StateFlow 1com.example.habits9.ui.settings.SettingsViewModel  String 1com.example.habits9.ui.settings.SettingsViewModel  UserPreferencesRepository 1com.example.habits9.ui.settings.SettingsViewModel  Build com.example.uhabits_99  MainActivity com.example.uhabits_99  Build #com.example.uhabits_99.MainActivity  Bundle #com.example.uhabits_99.MainActivity  RequiresApi #com.example.uhabits_99.MainActivity  DarkAccentPrimary com.example.uhabits_99.ui.theme  DarkBackground com.example.uhabits_99.ui.theme  DarkColorScheme com.example.uhabits_99.ui.theme  DarkDivider com.example.uhabits_99.ui.theme  DarkSurfaceVariant com.example.uhabits_99.ui.theme  DarkTextPrimary com.example.uhabits_99.ui.theme  DarkTextSecondary com.example.uhabits_99.ui.theme  LightAccentPrimary com.example.uhabits_99.ui.theme  LightBackground com.example.uhabits_99.ui.theme  LightColorScheme com.example.uhabits_99.ui.theme  LightDivider com.example.uhabits_99.ui.theme  LightSurfaceVariant com.example.uhabits_99.ui.theme  LightTextPrimary com.example.uhabits_99.ui.theme  LightTextSecondary com.example.uhabits_99.ui.theme  Pink40 com.example.uhabits_99.ui.theme  Pink80 com.example.uhabits_99.ui.theme  Purple40 com.example.uhabits_99.ui.theme  Purple80 com.example.uhabits_99.ui.theme  PurpleGrey40 com.example.uhabits_99.ui.theme  PurpleGrey80 com.example.uhabits_99.ui.theme  
Typography com.example.uhabits_99.ui.theme  FirebaseAuth com.google.firebase.auth  FirebaseFirestore com.google.firebase.firestore  Binds dagger  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  HiltViewModelMap &dagger.hilt.android.internal.lifecycle  KeySet 7dagger.hilt.android.internal.lifecycle.HiltViewModelMap  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  OriginatingElement dagger.hilt.codegen  SingletonComponent dagger.hilt.components  GeneratedEntryPoint dagger.hilt.internal  IntoMap dagger.multibindings  IntoSet dagger.multibindings  	StringKey dagger.multibindings  Build 	java.lang  SingletonComponent 	java.lang  String 	java.lang  com 	java.lang  	LocalDate 	java.time  	Generated javax.annotation.processing  Inject javax.inject  	Singleton javax.inject  Boolean kotlin  Build kotlin  Double kotlin  Float kotlin  Int kotlin  Long kotlin  SingletonComponent kotlin  String kotlin  Unit kotlin  com kotlin  Build kotlin.annotation  SingletonComponent kotlin.annotation  com kotlin.annotation  Build kotlin.collections  List kotlin.collections  Map kotlin.collections  SingletonComponent kotlin.collections  com kotlin.collections  Build kotlin.comparisons  SingletonComponent kotlin.comparisons  com kotlin.comparisons  Build 	kotlin.io  SingletonComponent 	kotlin.io  com 	kotlin.io  Build 
kotlin.jvm  SingletonComponent 
kotlin.jvm  com 
kotlin.jvm  Build 
kotlin.ranges  SingletonComponent 
kotlin.ranges  com 
kotlin.ranges  KClass kotlin.reflect  Build kotlin.sequences  SingletonComponent kotlin.sequences  com kotlin.sequences  Build kotlin.text  SingletonComponent kotlin.text  com kotlin.text  Flow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  HabitReorderUiState androidx.lifecycle.ViewModel  
HabitSortType androidx.lifecycle.ViewModel  
HabitSortType com.example.habits9.data  
HabitSortType 2com.example.habits9.data.UserPreferencesRepository  List 2com.example.habits9.data.UserPreferencesRepository  HabitSectionRepository $com.example.habits9.ui.MainViewModel  
HabitSortType $com.example.habits9.ui.MainViewModel  
AccentPrimary #com.example.habits9.ui.habitreorder  BackgroundDark #com.example.habits9.ui.habitreorder  DividerColor #com.example.habits9.ui.habitreorder  HabitReorderUiState #com.example.habits9.ui.habitreorder  HabitReorderViewModel #com.example.habits9.ui.habitreorder  Int #com.example.habits9.ui.habitreorder  SurfaceVariantDark #com.example.habits9.ui.habitreorder  TextPrimary #com.example.habits9.ui.habitreorder  
TextSecondary #com.example.habits9.ui.habitreorder  HabitReorderUiState 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  HabitRepository 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  Inject 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  Int 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  	StateFlow 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  UserPreferencesRepository 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  Map com.example.habits9.data  Boolean (com.example.habits9.data.HabitRepository  Int (com.example.habits9.data.HabitRepository  Map (com.example.habits9.data.HabitRepository  String (com.example.habits9.data.HabitRepository  Boolean 2com.example.habits9.data.HabitRepository.Companion  Int 2com.example.habits9.data.HabitRepository.Companion  Map 2com.example.habits9.data.HabitRepository.Companion  String 2com.example.habits9.data.HabitRepository.Companion  List #com.example.habits9.ui.habitreorder  Habit 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  List 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  GeneralInfo androidx.lifecycle.ViewModel  HabitAnalyticsUiState androidx.lifecycle.ViewModel  HabitAnalyticsUseCase androidx.lifecycle.ViewModel  MeasurableHabitAnalytics androidx.lifecycle.ViewModel  
TimePeriod androidx.lifecycle.ViewModel  YesNoHabitAnalytics androidx.lifecycle.ViewModel  Any "com.example.habits9.data.analytics  Boolean "com.example.habits9.data.analytics  ChartDataPoint "com.example.habits9.data.analytics  Double "com.example.habits9.data.analytics  Float "com.example.habits9.data.analytics  GeneralInfo "com.example.habits9.data.analytics  HabitAnalyticsRepository "com.example.habits9.data.analytics  HabitAnalyticsUseCase "com.example.habits9.data.analytics  Int "com.example.habits9.data.analytics  List "com.example.habits9.data.analytics  Long "com.example.habits9.data.analytics  Map "com.example.habits9.data.analytics  MeasurableHabitAnalytics "com.example.habits9.data.analytics  String "com.example.habits9.data.analytics  
TimePeriod "com.example.habits9.data.analytics  YesNoHabitAnalytics "com.example.habits9.data.analytics  Any ;com.example.habits9.data.analytics.HabitAnalyticsRepository  GeneralInfo ;com.example.habits9.data.analytics.HabitAnalyticsRepository  HabitAnalyticsUseCase ;com.example.habits9.data.analytics.HabitAnalyticsRepository  	HabitType ;com.example.habits9.data.analytics.HabitAnalyticsRepository  Inject ;com.example.habits9.data.analytics.HabitAnalyticsRepository  Long ;com.example.habits9.data.analytics.HabitAnalyticsRepository  MeasurableHabitAnalytics ;com.example.habits9.data.analytics.HabitAnalyticsRepository  
TimePeriod ;com.example.habits9.data.analytics.HabitAnalyticsRepository  YesNoHabitAnalytics ;com.example.habits9.data.analytics.HabitAnalyticsRepository  analyticsUseCase ;com.example.habits9.data.analytics.HabitAnalyticsRepository  Any 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  Boolean 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  ChartDataPoint 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  
Completion 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  CompletionRepository 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  Double 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  Float 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  GeneralInfo 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  Habit 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  HabitRepository 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  Inject 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  Int 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  List 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  	LocalDate 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  Long 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  Map 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  String 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  
TimePeriod 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getAveragePerCompletion 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  
getBestDay 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getCalendarData 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getCompletionHistory 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getCompletionRate 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getCurrentStreak 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getLongestStreak 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getTotalAmount 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getTotalCompletions 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  HabitAnalyticsUiState com.example.habits9.ui.details  HabitAnalyticsViewModel com.example.habits9.ui.details  Long com.example.habits9.ui.details  GeneralInfo 6com.example.habits9.ui.details.HabitAnalyticsViewModel  HabitAnalyticsUiState 6com.example.habits9.ui.details.HabitAnalyticsViewModel  HabitAnalyticsUseCase 6com.example.habits9.ui.details.HabitAnalyticsViewModel  	HabitType 6com.example.habits9.ui.details.HabitAnalyticsViewModel  Inject 6com.example.habits9.ui.details.HabitAnalyticsViewModel  Long 6com.example.habits9.ui.details.HabitAnalyticsViewModel  MeasurableHabitAnalytics 6com.example.habits9.ui.details.HabitAnalyticsViewModel  	StateFlow 6com.example.habits9.ui.details.HabitAnalyticsViewModel  
TimePeriod 6com.example.habits9.ui.details.HabitAnalyticsViewModel  YesNoHabitAnalytics 6com.example.habits9.ui.details.HabitAnalyticsViewModel  Any kotlin  HabitDetailsUiState androidx.lifecycle.ViewModel  HabitDetailsUiState com.example.habits9.ui.details  HabitDetailsViewModel com.example.habits9.ui.details  String com.example.habits9.ui.details  GeneralInfo 4com.example.habits9.ui.details.HabitDetailsViewModel  Habit 4com.example.habits9.ui.details.HabitDetailsViewModel  HabitAnalyticsUseCase 4com.example.habits9.ui.details.HabitDetailsViewModel  HabitDetailsUiState 4com.example.habits9.ui.details.HabitDetailsViewModel  HabitRepository 4com.example.habits9.ui.details.HabitDetailsViewModel  	HabitType 4com.example.habits9.ui.details.HabitDetailsViewModel  Inject 4com.example.habits9.ui.details.HabitDetailsViewModel  Long 4com.example.habits9.ui.details.HabitDetailsViewModel  MeasurableHabitAnalytics 4com.example.habits9.ui.details.HabitDetailsViewModel  	StateFlow 4com.example.habits9.ui.details.HabitDetailsViewModel  String 4com.example.habits9.ui.details.HabitDetailsViewModel  
TimePeriod 4com.example.habits9.ui.details.HabitDetailsViewModel  UserPreferencesRepository 4com.example.habits9.ui.details.HabitDetailsViewModel  YesNoHabitAnalytics 4com.example.habits9.ui.details.HabitDetailsViewModel  	LocalDate 4com.example.habits9.ui.details.HabitDetailsViewModel  CompletionRepository (com.example.habits9.data.HabitRepository  CompletionRepository 2com.example.habits9.data.HabitRepository.Companion  Long "com.example.habits9.ui.createhabit  Long 7com.example.habits9.ui.createhabit.CreateHabitViewModel  Unit com.example.habits9.ui.details  Unit 4com.example.habits9.ui.details.HabitDetailsViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          