# Prompt 5.3: Implement Habit Update and Delete Operations

## A. The Objective & Context

The goal of this task is to implement the remaining **Update** and **Delete** functionality for habits, completing the core CRUD operations. The user should be able to edit an existing habit from the details screen and delete it after a confirmation.

The entry points for these actions (the "Edit" icon and "Delete" menu option) are already present in the UI. This task involves building the logic and navigation flows to make them fully functional.

## B. Detailed Implementation Plan

### 1. Implement the "Update Habit" Flow

#### 1.1. Pre-fill the Edit Screen
- When the user taps the "Edit" icon in the `ShowHabitActivity` toolbar, the app must navigate to the `CreateHabitActivity` (or the equivalent screen used for creating habits).
- **CRITICAL**: The `CreateHabitActivity` must be launched in an "edit mode." It must be pre-populated with all the details of the habit being edited (name, description, color, frequency rules, etc.). You should pass the `habitId` to this activity to fetch the existing data.

#### 1.2. Implement Update Logic
- In the `CreateHabitActivity`'s ViewModel, add logic to handle the update operation.
- When the user clicks the "Save" button in "edit mode," the system must **update the existing habit record** in the database. It must **not** create a new habit.
- After the update is successfully saved to the database, the user should be navigated back to the `ShowHabitActivity` to see their changes reflected.

### 2. Implement the "Delete Habit" Flow

#### 2.1. Implement the Confirmation Dialog
- When the user taps the "Delete" option from the kebab menu in `ShowHabitActivity`, a confirmation dialog must be displayed.
- **Dialog Title**: "Delete Habit?"
- **Dialog Message**: "Are you sure you want to delete this habit? All its history will be permanently lost. This action cannot be undone."
- **Dialog Buttons**:
    - **"Delete"** (Confirm button, perhaps styled in red to indicate a destructive action).
    - **"Cancel"** (Dismiss button).

#### 2.2. Implement Deletion Logic
- If the user taps "Cancel," the dialog must be dismissed with no further action.
- If the user taps "Delete," the ViewModel must trigger a repository call to **permanently delete the habit** and all its associated completion records from the database.
- After the deletion is complete, the `ShowHabitActivity` must be closed, and the user should be navigated back to the main home screen (`MainActivity`).

## C. Meticulous Verification Plan

### 1. Update Flow Verification
1.  Navigate to the details screen for a specific habit (e.g., "Read a Book").
2.  Tap the "Edit" icon.
3.  **CRITICAL**: Verify that the "Create Habit" screen opens and that all fields are correctly pre-filled with the details of "Read a Book."
4.  Change the name of the habit to "Read a Fiction Book" and change its color.
5.  Tap "Save."
6.  **CRITICAL**: Verify you are navigated back to the details screen and the title now shows "Read a Fiction Book."
7.  Go back to the main home screen and verify the habit's name and color have been updated there as well.

### 2. Delete Flow Verification
1.  Navigate to the details screen for a habit you want to delete.
2.  Tap the kebab menu, then tap "Delete."
3.  **CRITICAL**: Verify the confirmation dialog appears with the correct title and message.
4.  Tap "Cancel." Verify the dialog closes and you remain on the details screen.
5.  Tap "Delete" in the menu again. This time, tap "Delete" in the dialog.
6.  **CRITICAL**: Verify you are navigated back to the main home screen and the habit has been completely removed from the list.
7.  Restart the app to ensure the deletion was permanent.

## D. Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.