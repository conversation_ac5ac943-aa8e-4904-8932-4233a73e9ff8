
> Task :app:compileDebugKotlin
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/createhabit/CreateHabitViewModel.kt:224:51 Unresolved reference 'first'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/createhabit/CreateHabitViewModel.kt:270:99 Unresolved reference 'first'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/createmeasurablehabit/CreateMeasurableHabitScreen.kt:72:55 Smart cast to 'kotlin.Long' is impossible, because 'editingHabitId' is a delegated property.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/createmeasurablehabit/CreateMeasurableHabitScreen.kt:76:32 Unresolved reference 'notes'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/createyesnohabit/CreateYesNoHabitScreen.kt:70:55 Smart cast to 'kotlin.Long' is impossible, because 'editingHabitId' is a delegated property.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/createyesnohabit/CreateYesNoHabitScreen.kt:74:32 Unresolved reference 'notes'.

> Task :app:compileDebugKotlin FAILED

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 13s
30 actionable tasks: 2 executed, 4 from cache, 24 up-to-date